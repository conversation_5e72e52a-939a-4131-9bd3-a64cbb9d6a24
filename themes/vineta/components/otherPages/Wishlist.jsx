"use client";
import { useContextElement } from "@/context/Context";
import React, { useEffect, useState } from "react";
import ProductCard4 from "../../components/productCards/ProductCard4";
import Link from "next/link";
import Sidebar from "../dashboard/Sidebar";


export default function Wishlist(productList) {
  const { wishList } = useContextElement();
  const [items, setItems] = useState(productList);

  useEffect(() => {
    setItems([...allProducts.filter((elm) => wishList.includes(elm.id))]);
  }, [wishList]);
  return (
    <div className="flat-spacing-13">
      <div className="container-7">
        {/* sidebar-account */}
        <div className="btn-sidebar-mb d-lg-none">
          <button data-bs-toggle="offcanvas" data-bs-target="#mbAccount">
            <i className="icon icon-sidebar" />
          </button>
        </div>
        {/* /sidebar-account */}
        {/* Section-acount */}

        <div className="main-content-account">
          <div className="sidebar-account-wrap sidebar-content-wrap sticky-top d-lg-block d-none">
            <ul className="my-account-nav">
              <Sidebar />
            </ul>
          </div>
          <div className="my-acount-content account-orders">

            <section className="s-account flat-spacing-4 pt_0">
              <div className="container">
                <div className="row">
                  <div className="col-lg-12">
                    {items.length ? (
                      <div
                        className="wrapper-shop tf-grid-layout tf-col-2 lg-col-3 xl-col-4 style-1"
                        id="gridLayout"
                      >
                        {items.map((product, i) => (
                          <ProductCard4 key={i} product={product} />
                        ))}

                        {/* Pagination */}
                        <ul className="wg-pagination">
                          <li className="active">
                            <div className="pagination-item">1</div>
                          </li>
                          <li>
                            <a href="#" className="pagination-item">
                              2
                            </a>
                          </li>
                          <li>
                            <a href="#" className="pagination-item">
                              3
                            </a>
                          </li>
                          <li>
                            <a href="#" className="pagination-item">
                              <i className="icon-arr-right2" />
                            </a>
                          </li>
                        </ul>
                      </div>
                    ) : (
                      <div className="">
                        <div>
                          İstek listeniz boş! İlgilendiğiniz ürünlere kolayca ulaşabilmek için istek listesine ekleyin!
                        </div>{" "}
                        <Link className="tf-btn btn-dark2 animate-btn mt-3" href="/urunler"  >
                          Ürünleri Keşfet
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
