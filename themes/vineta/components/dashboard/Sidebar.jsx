"use client";
import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
const accountLinks = [
  { href: "/hesabim/siparislerim", label: "Sipar<PERSON>şlerim" },
  { href: "/hesabim/istek-listem", label: "İstek Listem" },
  { href: "/hesabim/adreslerim", label: "Adreslerim" },
];

export default function Sidebar() {
  const pathname = usePathname();
  return (
    <>
      {accountLinks.map(({ href, label }) => (
        <li key={href}>
          <Link
            href={href}
            className={`text-sm link fw-medium my-account-nav-item ${pathname == href ? "active" : ""
              }`}
          >
            {label}
          </Link>
        </li>
      ))}
      <li>
        <Link href={`/`} className="text-sm link fw-medium my-account-nav-item">
          <PERSON><PERSON><PERSON><PERSON>ş Ya<PERSON>
        </Link>
      </li>
    </>
  );
}
