"use client";
import React from "react";
import Sidebar from "./Sidebar";
import Link from "next/link";
import Image from "next/image";
import CountdownTimer from "../common/Countdown";
import { useSession } from "next-auth/react";

export default function Account() {
  const { data: session } = useSession();
  return (
    <div className="flat-spacing-13">
      <div className="container-7">
        {/* sidebar-account */}
        <div className="btn-sidebar-mb d-lg-none">
          <button data-bs-toggle="offcanvas" data-bs-target="#mbAccount">
            <i className="icon icon-sidebar" />
          </button>
        </div>
        {/* /sidebar-account */}
        {/* Section-acount */}

        <div className="main-content-account">
          <div className="sidebar-account-wrap sidebar-content-wrap sticky-top d-lg-block d-none">
            <ul className="my-account-nav">
              <Sidebar />
            </ul>
          </div>
          <div className="my-acount-content account-dashboard">
            <div className="box-account-title">
              <p className="hello-name display-sm fw-medium">
                Merhaba {session?.user?.name || 'Değerli Müşterimiz'}!
              </p>
              <p className="notice text-sm">
                Hesabınıza ait bilgileri burada bulabilirsiniz.{" "}
                <a href="/hesabim/siparislerim" className="text-primary text-decoration-underline">
                  Son Siparişlerinize
                </a>
                {", "}
                <a href="/hesabim/istek-listem" className="text-primary text-decoration-underline">
                  İstek Listenize
                </a>{" "}
                veya {" "}
                <a href="/hesabim/kuponlarim" className="text-primary text-decoration-underline">
                  Kullanabileceğiniz kuponlara
                </a>{" "}
                buradan ulaşabilirsiniz.
              </p>
            </div>
            <div className="content-account">
              <ul className="box-check-list flex-sm-nowrap">
                <li>
                  <Link
                    href={`/hesabim/siparislerim`}
                    className="box-check text-center"
                  >
                    <div className="icon">
                      <i className="icon-order" />
                      <span className="count-number text-sm text-white fw-medium">
                        1
                      </span>
                    </div>
                    <div className="text">
                      <div className="link name-type text-xl fw-medium">
                        Siparişlerim
                      </div>
                      <p className="sub-type text-sm">
                        Vermiş olduğunuz siparişlere göz atın.
                      </p>
                    </div>
                  </Link>
                </li>
                <li>
                  <Link href={`/hesabim/istek-listem`} className="box-check text-center">
                    <div className="icon">
                      <i className="icon-heart" />
                      <span className="count-number text-sm text-white fw-medium">
                        1
                      </span>
                    </div>
                    <div className="text">
                      <div className="link name-type text-xl fw-medium">
                        İstek Listem
                      </div>
                      <p className="sub-type text-sm">İstek Listenize göz atın</p>
                    </div>
                  </Link>
                </li>
              </ul>

              <div className="banner-account">
                <div className="image">
                  <Image src="/images/banner/account-1.jpg" alt="" className="lazyload" width={912} height={280} />
                </div>
                <div className="banner-content-right">
                  <div className="banner-title">
                    <p className="display-md fw-medium">Ücretsiz Kargo</p>
                    <p className="text-md">Tüm ürünlerde geçerlidir.</p>
                  </div>
                  <div className="banner-btn">
                    <Link href={`/urunler`} className="tf-btn animate-btn">
                      Ürünlere Göz Atın
                    </Link>
                  </div>
                </div>
              </div>

              {/* <div className="banner-account banner-acc-countdown bg-linear d-flex align-items-center"> */}
              {/*   <div className="banner-content-left"> */}
              {/*     <div className="banner-title"> */}
              {/*       <p className="sub text-md fw-medium">SUMMER SALE</p> */}
              {/*       <p className="display-xl fw-medium">50% OFF</p> */}
              {/*       <p className="sub text-md fw-medium"> */}
              {/*         WITH PROMOTE CODE: 12D34E */}
              {/*       </p> */}
              {/*     </div> */}
              {/*     <div className="banner-btn"> */}
              {/*       <Link href={`/shop-default`} className="tf-btn btn-white animate-btn animate-dark"  > */}
              {/*         Shop Now */}
              {/*       </Link> */}
              {/*     </div> */}
              {/*   </div> */}
              {/*   <div className="banner-countdown"> */}
              {/*     <div className="wg-countdown-2"> */}
              {/*       <span className="js-countdown"> */}
              {/*         <CountdownTimer style={2} /> */}
              {/*       </span> */}
              {/*     </div> */}
              {/*   </div> */}
              {/* </div> */}

            </div>
          </div>
        </div>
      </div>
      {/* /Account */}
    </div>
  );
}
