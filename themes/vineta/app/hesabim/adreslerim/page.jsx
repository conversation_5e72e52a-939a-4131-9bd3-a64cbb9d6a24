import Breadcumb from "@/components/common/Breadcumb";
import Address from "@/components/dashboard/Address";
import React from "react";

/**
 * Metadata yapısı
 * @typedef {Object} Metadata
 * @property {string} title - Başlık
 * @property {string} description - Açıklama
 */

/**
 * <PERSON>res yapısı
 * @typedef {Object} Address
 * @property {number} id - Benzersiz adres tanımlayıcısı
 * @property {string} firstName - Adres sahibinin adı
 * @property {string} lastName - Adres sahibinin soyadı
 * @property {string} company - Şirket adı
 * @property {string} address1 - Adres satırı 1
 * @property {string} city - Şehir
 * @property {string} region - Bölge
 * @property {string} province - İl
 * @property {string} zipCode - Posta kodu
 * @property {string} phone - Telefon numarası
 * @property {boolean} isDefault - Varsayılan adres mi?
 * @property {string} email - E-posta adresi
 */

/**
 * Adres listesi ve metadata bilgilerini içeren yapı 
 * @typedef {Object} AddressData
 * @property {Address[]} addresses - <PERSON><PERSON> listesi
 * @property {Metadata} metadata - Metadata bilgileri
 */

/**
 * @type {AddressData}
 */

/* 
const accountAddressesRequests = {
  // Adresleri getir
  getAddresses: () => api.get('/account/addresses'),
    
  // Adres ekle
  addAddress: (address) => api.post('/account/addresses', address),
  
  // Adres sil
  removeFromWishlist: (addressId) => 
    api.delete(`/account/addresses/${addressId}`)
};
 */

export const metadata = {
  title: "Accout Address || Vineta - Multipurpose React Nextjs eCommerce",
  description: "Vineta - Multipurpose React Nextjs eCommerce",
};
export default function page() {
  return (
    <>
      <Breadcumb pageName="Addresses" pageTitle="Addresses" />
      <Address />
    </>
  );
}
