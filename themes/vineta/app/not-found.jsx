import React from "react";
import Link from "next/link";
import Image from "next/image";
export const metadata = {
  title: "Sayfa Bulunamadı || Dermedic",
  description: "<PERSON><PERSON>s ciltler için ürünlerimize göz atın.",
};
export default function page() {
  return (
    <>
      <section className="flat-spacing">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="wg-404">
                <div className="image">
                  <Image
                    src="/images/banner/404.png"
                    alt={404}
                    className="lazyload"
                    width={472}
                    height={472}
                  />
                </div>
                <p className="title">Sayfa Bulunamadı!</p>
                <p className="text-md sub text-main">
                  Girmeye çalıştığınız sayfa yanlış ya da kırık. Lütfen anasayfaya dönünüz.
                </p>
                <div className="bot">
                  <Link href={`/`} className="tf-btn btn-fill animate-btn">
                    Ana<PERSON><PERSON>'ya dön
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
