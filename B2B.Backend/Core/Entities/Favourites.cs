using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities;
[Table("Favourites")]
public partial class Favourite : BaseEntity
{
    public Guid CustomerId { get; set; }
    public Customer Customer { get; set; } = null!;
    public ICollection<Product> Products { get; set; } = [];

    public static void Configure(EntityTypeBuilder<Favourite> builder)
    {
        builder.HasOne(f => f.Customer)
            .WithMany(u => u.Favourites)
            .HasForeignKey(f => f.CustomerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(f => f.Products)
            .WithOne(p => p.Favourite)
            .HasForeignKey(f => f.FavouriteId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

[Table("FavouritesHistory")]
public class FavouriteHistory : HistoryBaseEntity
{
    // Entity properties
    public Guid CustomerId { get; set; }
    public Guid ProductId { get; set; }
}
