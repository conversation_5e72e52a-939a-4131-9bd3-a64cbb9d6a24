using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Core.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities;

[Table("Products")]
public partial class Product : BaseEntity
{
    [Required, MaxLength(256)]
    public string Name { get; set; } = null!;

    [Required, MaxLength(256)]
    public string Slug { get; set; } = null!;

    public string? Description { get; set; }
    /// <summary>
    /// ProductType Değeri,
    /// Müşterilere gösterilecekse Simple,
    /// Varyant grubunun ana ürünüyse Variant,
    /// Birden çok ürünün satıldığı paket ise Grouped seçilmelidir.
    /// </summary>
    [Required] public ProductType ProductType { get; set; }



    [MaxLength(64)]
    public string? Sku { get; set; }

    [MaxLength(64)]
    public string? Barcode { get; set; }

    public decimal? Price { get; set; }

    public int? StockQuantity { get; set; }

    /// <summary>
    /// Ürünün hacim bilgisi (ml, gr, kg vb.)
    /// </summary>
    public decimal? Volume { get; set; }

    /// <summary>
    /// Hacim birimi (ml, gr, kg, adet vb.)
    /// </summary>
    [MaxLength(10)]
    public string? VolumeUnit { get; set; }

    /// <summary>
    /// Ürünün hacim varyantları olup olmadığını belirtir
    /// </summary>
    public bool HasVolumeVariants { get; set; } = false;

    public Guid? ParentProductId { get; set; }
    public Product? Parent { get; set; }
    public Guid? CategoryId { get; set; }
    public ProductCategory? Category { get; set; }
    public Guid? BrandId { get; set; }
    public ProductBrand? Brand { get; set; }
    public ProductSeo? Seo { get; set; }
    public Favourite Favourite { get; set; } = new Favourite();
    public Guid? FavouriteId { get; set; }
    public ICollection<Product> Variants { get; set; } = new List<Product>();
    public ICollection<ProductImage> Images { get; set; } = new List<ProductImage>();
    public ICollection<ProductFaq> Faqs { get; set; } = new List<ProductFaq>();
    public ICollection<ProductReview> Reviews { get; set; } = new List<ProductReview>();
    public ICollection<ProductAttributeMapping> AttributeMappings { get; set; } = new List<ProductAttributeMapping>();
    public ICollection<OrderRow> OrderRows { get; set; } = new List<OrderRow>();
    public ICollection<ProductPrice> Prices { get; set; } = new List<ProductPrice>();
    public ICollection<Stock> Stocks { get; set; } = new List<Stock>();
    public ICollection<CartItem> CartItems { get; set; } = new List<CartItem>();
    public ICollection<ProductVolume> Volumes { get; set; } = new List<ProductVolume>();

    #region Fluent Configuration
    public static void Configure(EntityTypeBuilder<Product> builder)
    {
        builder.HasIndex(p => p.Slug).IsUnique();

        builder.Property(p => p.Price)
            .HasPrecision(18, 2);

        builder.HasOne(p => p.Parent)
            .WithMany(p => p.Variants)
            .HasForeignKey(p => p.ParentProductId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(p => p.Category)
            .WithMany(c => c.Products)
            .HasForeignKey(p => p.CategoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(p => p.Brand)
            .WithMany(b => b.Products)
            .HasForeignKey(p => p.BrandId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(p => p.Seo)
            .WithOne(s => s.Product)
            .HasForeignKey<ProductSeo>(s => s.ProductId)
            .OnDelete(DeleteBehavior.Cascade);
    }
    #endregion
}

[Table("ProductsHistory")]
public class ProductHistory : HistoryBaseEntity
{
    // Entity properties
    public string Name { get; set; } = null!;
    public string Slug { get; set; } = null!;
    public string? Description { get; set; }
    public ProductType ProductType { get; set; }
    public Guid? ParentProductId { get; set; }
    public Guid? CategoryId { get; set; }
    public string? Sku { get; set; }
    public string? Barcode { get; set; }
    public decimal? Price { get; set; }
    public int? StockQuantity { get; set; }
}
