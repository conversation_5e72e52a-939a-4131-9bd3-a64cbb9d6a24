using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Infrastructure.Data;
using Infrastructure.Services;
using Infrastructure.Repositories;
using Infrastructure.Data.Repositories;
using Infrastructure.Data.Repositories.Interfaces;
using Application.Contracts.Interfaces;
using Application.Contracts.Services;
using Application.Contracts.Repositories;
using Core.Interfaces;
using Core.Entities;
using Microsoft.AspNetCore.Identity;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
var configuration = builder.Configuration;
builder.Services.AddOpenApi();

// Database configuration
builder.Services.AddDbContext<B2BDbContext>(options =>
    options.UseNpgsql(configuration.GetConnectionString("VeriTabani")));

// CORS yapılandırması - müşteri tarafı için
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowCustomerFrontend", policy =>
    {
        policy
            .WithOrigins("http://localhost:3000", "http://localhost:3001")
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials()
            .WithExposedHeaders("Content-Disposition");
    });
});

// JWT Bearer Authentication - müşteri token'ları için
builder.Services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.RequireHttpsMetadata = false;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = false,
            ValidateAudience = false,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["NextAuth:Secret"]!)),
            ValidateLifetime = true,
            ClockSkew = TimeSpan.FromMinutes(5),
            NameClaimType = "name",
            RoleClaimType = "roles"
        };
    });

// HttpContextAccessor ve CurrentUserService kayıtları
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();

// Password Hasher for Customer
builder.Services.AddScoped<IPasswordHasher<Customer>, PasswordHasher<Customer>>();

// Repository kayıtları
builder.Services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepository<>));
builder.Services.AddScoped<ICustomerRepository, CustomerRepository>();

// Encryption Service
builder.Services.AddScoped<IEncryptionService, EncryptionService>();

// B2C Customer Services kayıtları
builder.Services.AddScoped<ICustomerService, CustomerService>();
builder.Services.AddScoped<ICustomerProductService, CustomerProductService>();
builder.Services.AddScoped<ICustomerCategoryService, CustomerCategoryService>();
builder.Services.AddScoped<IProductVolumeService, ProductVolumeService>();

// Customer Authentication Services
builder.Services.AddScoped<ICustomerAuthService, CustomerAuthService>();

// Campaign Services
builder.Services.AddScoped<ICampaignRepository, CampaignRepository>();
builder.Services.AddScoped<ICampaignService, CampaignService>();
builder.Services.AddScoped<ICampaignCalculationService, CampaignCalculationService>();

// Company Info Services
builder.Services.AddScoped<ICompanyInfoService, CompanyInfoService>();
builder.Services.AddScoped<ICompanyInfoRepository, CompanyInfoRepository>();

builder.Services.AddControllers();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

// CORS middleware
app.UseCors("AllowCustomerFrontend");
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

app.Run();
